#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动填充模板程序
根据template.csv的表头，从品牌信息.csv中提取对应数据并填入模板

作者: Python开发猫娘 🐾
功能: 智能匹配表头，保持数据行完整性
"""

import pandas as pd
import os
from typing import List, Dict, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_fill.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TemplateAutoFiller:
    """模板自动填充器"""
    
    def __init__(self, template_path: str, source_path: str, output_path: str = None):
        """
        初始化填充器
        
        Args:
            template_path: 模板文件路径
            source_path: 源数据文件路径  
            output_path: 输出文件路径，默认为template_filled.csv
        """
        self.template_path = template_path
        self.source_path = source_path
        self.output_path = output_path or template_path.replace('.csv', '_filled.csv')
        
        # 数据容器
        self.template_headers: List[str] = []
        self.source_headers: List[str] = []
        self.matched_headers: Dict[str, str] = {}
        self.source_data: pd.DataFrame = None
        
    def load_template_headers(self) -> List[str]:
        """
        加载模板文件的表头
        
        Returns:
            表头列表
        """
        try:
            # 只读取第一行获取表头
            template_df = pd.read_csv(self.template_path, nrows=0, encoding='utf-8')
            self.template_headers = template_df.columns.tolist()
            logger.info(f"📋 模板表头加载成功: {len(self.template_headers)}个字段")
            logger.info(f"表头: {self.template_headers}")
            return self.template_headers
            
        except Exception as e:
            logger.error(f"❌ 加载模板表头失败: {e}")
            raise
    
    def load_source_data(self) -> pd.DataFrame:
        """
        加载源数据文件
        
        Returns:
            源数据DataFrame
        """
        try:
            self.source_data = pd.read_csv(self.source_path, encoding='utf-8')
            self.source_headers = self.source_data.columns.tolist()
            logger.info(f"📊 源数据加载成功: {len(self.source_data)}行 x {len(self.source_headers)}列")
            logger.info(f"源数据表头: {self.source_headers}")
            return self.source_data
            
        except Exception as e:
            logger.error(f"❌ 加载源数据失败: {e}")
            raise
    
    def match_headers(self) -> Dict[str, str]:
        """
        匹配模板表头与源数据表头
        
        Returns:
            匹配结果字典 {模板表头: 源数据表头}
        """
        self.matched_headers = {}
        unmatched_headers = []
        
        for template_header in self.template_headers:
            if template_header in self.source_headers:
                self.matched_headers[template_header] = template_header
                logger.info(f"✅ 表头匹配成功: '{template_header}'")
            else:
                unmatched_headers.append(template_header)
                logger.warning(f"⚠️ 表头未匹配: '{template_header}'")
        
        logger.info(f"🎯 匹配结果: {len(self.matched_headers)}/{len(self.template_headers)} 个表头匹配成功")
        
        if unmatched_headers:
            logger.warning(f"未匹配的表头: {unmatched_headers}")
            logger.info("💡 提示: 请检查表头名称是否完全一致（包括空格、标点符号）")
        
        return self.matched_headers
    
    def extract_matched_data(self) -> pd.DataFrame:
        """
        提取匹配的数据列
        
        Returns:
            提取的数据DataFrame
        """
        if not self.matched_headers:
            logger.error("❌ 没有匹配的表头，无法提取数据")
            return pd.DataFrame()
        
        try:
            # 只提取匹配的列，保持原始列名顺序
            matched_source_columns = [self.matched_headers[header] for header in self.template_headers if header in self.matched_headers]
            extracted_data = self.source_data[matched_source_columns].copy()
            
            # 重命名列为模板表头名称
            column_rename_map = {v: k for k, v in self.matched_headers.items()}
            extracted_data.rename(columns=column_rename_map, inplace=True)
            
            logger.info(f"📤 数据提取成功: {len(extracted_data)}行 x {len(extracted_data.columns)}列")
            return extracted_data
            
        except Exception as e:
            logger.error(f"❌ 数据提取失败: {e}")
            raise
    
    def create_filled_template(self, extracted_data: pd.DataFrame) -> pd.DataFrame:
        """
        创建填充后的模板
        
        Args:
            extracted_data: 提取的数据
            
        Returns:
            填充后的DataFrame
        """
        try:
            # 创建空的模板DataFrame，包含所有模板表头
            filled_template = pd.DataFrame(columns=self.template_headers)
            
            # 填入匹配的数据
            for column in extracted_data.columns:
                if column in filled_template.columns:
                    filled_template[column] = extracted_data[column]
            
            # 未匹配的列保持为空
            for column in self.template_headers:
                if column not in extracted_data.columns:
                    filled_template[column] = ''
            
            logger.info(f"✨ 模板填充完成: {len(filled_template)}行数据")
            return filled_template
            
        except Exception as e:
            logger.error(f"❌ 模板填充失败: {e}")
            raise
    
    def save_result(self, filled_data: pd.DataFrame) -> str:
        """
        保存填充结果
        
        Args:
            filled_data: 填充后的数据
            
        Returns:
            输出文件路径
        """
        try:
            filled_data.to_csv(self.output_path, index=False, encoding='utf-8')
            logger.info(f"💾 结果保存成功: {self.output_path}")
            logger.info(f"📊 输出数据: {len(filled_data)}行 x {len(filled_data.columns)}列")
            return self.output_path
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            raise
    
    def run(self) -> str:
        """
        执行完整的填充流程
        
        Returns:
            输出文件路径
        """
        logger.info("🚀 开始执行模板自动填充...")
        
        try:
            # 1. 加载模板表头
            self.load_template_headers()
            
            # 2. 加载源数据
            self.load_source_data()
            
            # 3. 匹配表头
            self.match_headers()
            
            if not self.matched_headers:
                logger.error("❌ 没有匹配的表头，程序终止")
                return None
            
            # 4. 提取匹配的数据
            extracted_data = self.extract_matched_data()
            
            # 5. 创建填充后的模板
            filled_template = self.create_filled_template(extracted_data)
            
            # 6. 保存结果
            output_path = self.save_result(filled_template)
            
            logger.info("🎉 模板填充完成！")
            return output_path
            
        except Exception as e:
            logger.error(f"💥 程序执行失败: {e}")
            raise


def main():
    """主函数"""
    # 文件路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, 'template.csv')
    source_path = os.path.join(current_dir, '品牌信息.csv')
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        logger.error(f"❌ 模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(source_path):
        logger.error(f"❌ 源数据文件不存在: {source_path}")
        return
    
    # 创建填充器并执行
    filler = TemplateAutoFiller(template_path, source_path)
    
    try:
        output_path = filler.run()
        if output_path:
            print(f"\n🎉 填充完成！输出文件: {output_path}")
            print(f"📊 匹配的表头数量: {len(filler.matched_headers)}")
            print(f"📋 总表头数量: {len(filler.template_headers)}")
            
            # 显示匹配情况
            if filler.matched_headers:
                print("\n✅ 成功匹配的表头:")
                for template_header in filler.matched_headers:
                    print(f"  - {template_header}")
            
            unmatched = [h for h in filler.template_headers if h not in filler.matched_headers]
            if unmatched:
                print("\n⚠️ 未匹配的表头:")
                for header in unmatched:
                    print(f"  - {header}")
        
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")


if __name__ == "__main__":
    main()
