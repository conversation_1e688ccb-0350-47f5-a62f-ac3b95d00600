2025-07-31 14:10:12,039 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 14:10:12,042 - INFO - 📋 模板表头加载成功: 7个字段
2025-07-31 14:10:12,043 - INFO - 表头: ['店铺名称', '经营地点', '目标人群', '产出数量', '呼吁行动（私信咨询、评论区提问）', '新品名称', '产品特点']
2025-07-31 14:10:12,046 - INFO - 📊 源数据加载成功: 38行 x 33列
2025-07-31 14:10:12,047 - INFO - 源数据表头: ['行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品/服务优势', '售价', '目标人群/目标客户（例如上班族/90后/00后/宝妈/奶爸/二次元）', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 14:10:12,047 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-07-31 14:10:12,047 - INFO - ✅ 表头匹配成功: '经营地点'
2025-07-31 14:10:12,047 - WARNING - ⚠️ 表头未匹配: '目标人群'
2025-07-31 14:10:12,047 - WARNING - ⚠️ 表头未匹配: '产出数量'
2025-07-31 14:10:12,049 - WARNING - ⚠️ 表头未匹配: '呼吁行动（私信咨询、评论区提问）'
2025-07-31 14:10:12,049 - WARNING - ⚠️ 表头未匹配: '新品名称'
2025-07-31 14:10:12,049 - WARNING - ⚠️ 表头未匹配: '产品特点'
2025-07-31 14:10:12,049 - INFO - 🎯 匹配结果: 2/7 个表头匹配成功
2025-07-31 14:10:12,049 - WARNING - 未匹配的表头: ['目标人群', '产出数量', '呼吁行动（私信咨询、评论区提问）', '新品名称', '产品特点']
2025-07-31 14:10:12,049 - INFO - 💡 提示: 请检查表头名称是否完全一致（包括空格、标点符号）
2025-07-31 14:10:12,051 - INFO - 📤 数据提取成功: 38行 x 2列
2025-07-31 14:10:12,053 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 14:10:12,055 - INFO - 💾 结果保存成功: E:\python\过程文件夹\template_filled.csv
2025-07-31 14:10:12,056 - INFO - 📊 输出数据: 38行 x 7列
2025-07-31 14:10:12,056 - INFO - 🎉 模板填充完成！
2025-07-31 14:16:45,577 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 14:16:45,578 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 14:16:45,578 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 14:16:45,587 - INFO - 📋 模板表头加载成功: 7个字段
2025-07-31 14:16:45,587 - INFO - 表头: ['店铺名称', '经营地点', '目标人群', '产出数量', '呼吁行动（私信咨询、评论区提问）', '新品名称', '产品特点']
2025-07-31 14:16:45,648 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 14:16:45,649 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 14:16:45,658 - INFO - 📊 源数据加载成功: 38行 x 35列
2025-07-31 14:16:45,659 - INFO - 源数据表头: ['行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '产品/服务优势', '售价', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 14:16:45,659 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-07-31 14:16:45,659 - INFO - ✅ 表头匹配成功: '经营地点'
2025-07-31 14:16:45,659 - INFO - ✅ 表头匹配成功: '目标人群'
2025-07-31 14:16:45,659 - WARNING - ⚠️ 表头未匹配: '产出数量'
2025-07-31 14:16:45,659 - INFO - ✅ 表头匹配成功: '呼吁行动（私信咨询、评论区提问）'
2025-07-31 14:16:45,659 - INFO - ✅ 表头匹配成功: '新品名称'
2025-07-31 14:16:45,661 - INFO - ✅ 表头匹配成功: '产品特点'
2025-07-31 14:16:45,661 - INFO - 🎯 匹配结果: 6/7 个表头匹配成功
2025-07-31 14:16:45,661 - WARNING - 未匹配的表头: ['产出数量']
2025-07-31 14:16:45,661 - INFO - 💡 提示: 请检查表头名称是否完全一致（包括空格、标点符号）
2025-07-31 14:16:45,664 - INFO - 📤 数据提取成功: 38行 x 6列
2025-07-31 14:16:45,666 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 14:16:45,669 - INFO - 💾 结果保存成功: E:\python\过程文件夹\template_filled.csv
2025-07-31 14:16:45,669 - INFO - 📊 输出数据: 38行 x 7列
2025-07-31 14:16:45,670 - INFO - 🎉 模板填充完成！
2025-07-31 14:18:32,082 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 14:18:32,082 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 14:18:32,083 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 14:18:32,091 - INFO - 📋 模板表头加载成功: 7个字段
2025-07-31 14:18:32,092 - INFO - 表头: ['店铺名称', '经营地点', '目标人群', '产出数量', '呼吁行动（私信咨询、评论区提问）', '新品名称', '产品特点']
2025-07-31 14:18:32,155 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 14:18:32,155 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 14:18:32,161 - INFO - 📊 源数据加载成功: 38行 x 36列
2025-07-31 14:18:32,161 - INFO - 源数据表头: ['产出数量', '行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '产品/服务优势', '售价', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 14:18:32,161 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '经营地点'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '目标人群'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '产出数量'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '呼吁行动（私信咨询、评论区提问）'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '新品名称'
2025-07-31 14:18:32,162 - INFO - ✅ 表头匹配成功: '产品特点'
2025-07-31 14:18:32,162 - INFO - 🎯 匹配结果: 7/7 个表头匹配成功
2025-07-31 14:18:32,169 - INFO - 📤 数据提取成功: 38行 x 7列
2025-07-31 14:18:32,173 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 14:18:32,179 - INFO - 💾 结果保存成功: e:\python\过程文件夹\template_filled.csv
2025-07-31 14:18:32,179 - INFO - 📊 输出数据: 38行 x 7列
2025-07-31 14:18:32,180 - INFO - 🎉 模板填充完成！
